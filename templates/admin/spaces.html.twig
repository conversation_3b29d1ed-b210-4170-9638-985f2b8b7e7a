{% extends 'base.html.twig' %}

{% block title %}Admin - Spaces{% endblock %}

{% block content %}
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Space Management</h1>
            <div class="flex space-x-3">
                <a href="{{ path('app_space_new') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 transition-colors flex items-center font-medium shadow-md">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add New Space
                </a>
                <a href="{{ path('app_admin_dashboard') }}" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors flex items-center font-medium shadow-md">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Retourner au Dashboard
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-6 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Total Spaces</h3>
                <p class="text-3xl font-bold text-blue-600 dark:text-blue-500">{{ spaces|length }}</p>
            </div>
            <div class="bg-white rounded-lg shadow p-6 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Total Desks</h3>
                <p class="text-3xl font-bold text-green-600 dark:text-green-500">
                    {% set totalDesks = 0 %}
                    {% for space in spaces %}
                        {% set totalDesks = totalDesks + space.desks|length %}
                    {% endfor %}
                    {{ totalDesks }}
                </p>
            </div>
            <div class="bg-white rounded-lg shadow p-6 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Cities</h3>
                <p class="text-3xl font-bold text-purple-600 dark:text-purple-500">
                    {% set cities = [] %}
                    {% for space in spaces %}
                        {% if space.address and space.address.city not in cities %}
                            {% set cities = cities|merge([space.address.city]) %}
                        {% endif %}
                    {% endfor %}
                    {{ cities|length }}
                </p>
            </div>
            <div class="bg-white rounded-lg shadow p-6 dark:bg-gray-800">
                <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-white">Hosts</h3>
                <p class="text-3xl font-bold text-orange-600 dark:text-orange-500">
                    {% set hosts = [] %}
                    {% for space in spaces %}
                        {% if space.host and space.host.id not in hosts %}
                            {% set hosts = hosts|merge([space.host.id]) %}
                        {% endif %}
                    {% endfor %}
                    {{ hosts|length }}
                </p>
            </div>
        </div>

        <!-- Spaces Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">ID</th>
                            <th scope="col" class="px-6 py-3">Nom</th>
                            <th scope="col" class="px-6 py-3">Host</th>
                            <th scope="col" class="px-6 py-3">Localisation</th>
                            <th scope="col" class="px-6 py-3">Bureaux</th>
                            <th scope="col" class="px-6 py-3">Disponibilité</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for space in spaces %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">{{ space.id }}</td>
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900 dark:text-white">{{ space.name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ space.description|slice(0, 50) }}{% if space.description|length > 50 %}...{% endif %}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="font-medium text-gray-900 dark:text-white">{{ space.host.firstname }} {{ space.host.lastname }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ space.host.email }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    {% if space.address %}
                                        <div class="font-medium text-gray-900 dark:text-white">{{ space.address.city }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ space.address.street }}</div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">{{ space.address.postalCode }}</div>
                                    {% else %}
                                        <span class="text-red-500 dark:text-red-400">No address</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="font-medium text-gray-900 dark:text-white">{{ space.desks|length }}</span>
                                        <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">desks</span>
                                    </div>
                                    {% if space.desks|length > 0 %}
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {% set availableDesks = 0 %}
                                            {% for desk in space.desks %}
                                                {% if desk.isAvailable %}
                                                    {% set availableDesks = availableDesks + 1 %}
                                                {% endif %}
                                            {% endfor %}
                                            {{ availableDesks }} available
                                        </div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    {% if space.availability %}
                                        <div class="flex flex-wrap gap-1">
                                            {% if space.availability.isMonday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">L</span>{% endif %}
                                            {% if space.availability.isTuesday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">M</span>{% endif %}
                                            {% if space.availability.isWednesday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">Me</span>{% endif %}
                                            {% if space.availability.isThursday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">J</span>{% endif %}
                                            {% if space.availability.isFriday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">V</span>{% endif %}
                                            {% if space.availability.isSaturday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">S</span>{% endif %}
                                            {% if space.availability.isSunday %}<span class="px-1 py-0.5 bg-green-100 text-green-800 text-xs rounded dark:bg-green-900 dark:text-green-300">D</span>{% endif %}
                                        </div>
                                    {% else %}
                                        <span class="text-red-500 dark:text-red-400">Not set</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    {% if space.desks|length > 0 %}
                                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Active</span>
                                    {% else %}
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">No Desks</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ path('app_space_show', {'id': space.id}) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline" title="View Space">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>
                                        <a href="{{ path('app_space_edit', {'id': space.id}) }}" class="font-medium text-green-600 dark:text-green-500 hover:underline" title="Edit Space">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        <button data-modal-target="deleteModal-{{ space.id }}" data-modal-toggle="deleteModal-{{ space.id }}" class="font-medium text-red-600 dark:text-red-500 hover:underline" title="Delete Space">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="8" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                                    <div class="flex flex-col items-center">
                                        <svg class="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <p class="text-lg font-medium">No spaces found</p>
                                        <p class="text-sm">Get started by creating your first workspace space.</p>
                                        <a href="{{ path('app_space_new') }}" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                            Create First Space
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% for space in spaces %}
        <div id="deleteModal-{{ space.id }}" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
            <div class="relative p-4 w-full max-w-2xl max-h-full">
                <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                    <button type="button" class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="deleteModal-{{ space.id }}">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                    <div class="p-4 md:p-5 text-center">
                        <svg class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                        </svg>
                        <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
                            Are you sure you want to delete the space "<span class="font-medium text-gray-900 dark:text-white">{{ space.name }}</span>"?
                        </h3>
                        <p class="mb-5 text-sm text-gray-500 dark:text-gray-400">
                            This action cannot be undone and will also delete all associated desks and reservations.
                        </p>
                        <div class="items-center p-4 md:p-5 ">
                            <button data-modal-hide="deleteModal-{{ space.id }}" type="button" class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600">
                                No, cancel
                            </button>
                            <form method="POST" action="{{ path('app_admin_space_delete', {'id': space.id}) }}" class="inline">
                                <input type="hidden" name="_token" value="{{ csrf_token('delete_space') }}">
                                <input type="hidden" name="_method" value="DELETE">
                                <button type="submit" class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center me-2">
                                    Yes, I'm sure
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endfor %}
{% endblock %}